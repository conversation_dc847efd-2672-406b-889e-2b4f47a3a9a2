@import url("https://fonts.googleapis.com/css2?family=M+PLUS+1p&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Noto+Sans+JP:wght@100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  font-family: "Noto Sans JP", sans-serif;
  font-optical-sizing: auto;
}

html {
  scroll-behavior: smooth;
}

@layer base {
  :root {
    --background: 246 31% 13%;
    --blue01: 188 56% 38%;
    --blue02: 184 94% 75%;
    --purple01: 260 47% 63%;
    --gray01: 0 0% 45%;
    --golden: 39 100% 73%;
    --green01: 125 28% 48%;
    --sub: 247 52% 9%;
    --foreground: 0 0% 100%;
    --font: 0 0% 80%;
    --card: var(--sub);
    --card-foreground: 0 0% 100%;
    --red01: 4 100% 45%;
    --red02: 360, 64%, 49%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 100%;
    --primary: 36 89% 50%;
    --primary-foreground: 355.7 100% 97.3%;
    --tertiary: 27 100% 60%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 5.9% 90%;
    --ring: 348 98% 23%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: var(--sub);
    --sidebar-foreground: var(--font);
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --dark: 249 13% 19%;

    /* 
    --size-pc: 16px;
    --size-h2-pc: 48px;
    --size-h2-sub-title-pc: 20px;
    --size-h2-sp: 32px;
    --size-h2-sub-title-sp: 14px;
    --size-p-bold: 14px;
    --size-p-sp-bold: 16px;
    --size-p-pc-medium: 16px;
    --size-p-medium: 14px;
    --size-p-regular: 14px;

    --h-pc: 200px;
    --h-h2-pc: 160px;
    --h-h2-sub-title-pc: 170px;
    --h-h2-sp: 150px;
    --h-h2-sub-title-sp: 170px;
    --h-p-bold: 180px;
    --h-p-sp-bold: 200px;
    --h-p-pc-medium: 200px;
    --h-p-medium: 180px;
    --h-p-regular: 180px;

    --pc: 16px/200px;
    --h2-pc: 48px/160px;
    --h2-sub-title-pc: 20px/170px;
    --h2-sp: 32px/150px;
    --h2-sub-title-sp: 14px/170px;
    --p-bold: 14px/180px;
    --p-sp-bold: 16px/200px;
    --p-pc-medium: 16px/200px;
    --p-medium: 14px/180px;
    --p-regular: 14px/180px; */
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 348 98% 23%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 348 98% 23%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 27 100% 60%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 27 100% 60%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --dark: 240 6% 8%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

.rdp-vhidden {
  @apply hidden;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.ContentEditable__root {
  @apply max-h-96 h-96;
}

.EditorTheme__code {
  background-color: transparent;
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  overflow-x: auto;
  border: 1px solid #ccc;
  position: relative;
  border-radius: 8px;
  tab-size: 2;
}
.EditorTheme__code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: transparent;
  border-right: 1px solid #ccc;
  left: 0;
  top: 0;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}
.EditorTheme__table {
  border-collapse: collapse;
  border-spacing: 0;
  overflow-y: scroll;
  overflow-x: scroll;
  table-layout: fixed;
  width: fit-content;
  width: 100%;
  margin: 0px 25px 30px 0px;
}
.EditorTheme__tokenComment {
  color: slategray;
}
.EditorTheme__tokenPunctuation {
  color: #999;
}
.EditorTheme__tokenProperty {
  color: #905;
}
.EditorTheme__tokenSelector {
  color: #690;
}
.EditorTheme__tokenOperator {
  color: #9a6e3a;
}
.EditorTheme__tokenAttr {
  color: #07a;
}
.EditorTheme__tokenVariable {
  color: #e90;
}
.EditorTheme__tokenFunction {
  color: #dd4a68;
}

.Collapsible__container {
  background-color: var(--background);
  border: 1px solid #ccc;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.Collapsible__title {
  padding: 0.25rem;
  padding-left: 1rem;
  position: relative;
  font-weight: bold;
  outline: none;
  cursor: pointer;
  list-style-type: disclosure-closed;
  list-style-position: inside;
}

.Collapsible__title p {
  display: inline-flex;
}
.Collapsible__title::marker {
  color: lightgray;
}
.Collapsible__container[open] > .Collapsible__title {
  list-style-type: disclosure-open;
}

[type="page-break"] {
  position: relative;
  display: block;
  width: calc(100% + var(--editor-input-padding, 28px) * 2);
  overflow: unset;
  margin-left: calc(var(--editor-input-padding, 28px) * -1);
  margin-top: var(--editor-input-padding, 28px);
  margin-bottom: var(--editor-input-padding, 28px);

  border: none;
  border-top: 1px dashed var(--editor-color-secondary, #eeeeee);
  border-bottom: 1px dashed var(--editor-color-secondary, #eeeeee);
  background-color: var(--editor-color-secondary, #eeeeee);
}

[type="page-break"]::before {
  content: "";

  position: absolute;
  top: 50%;
  left: calc(var(--editor-input-padding, 28px) + 12px);
  transform: translateY(-50%);
  opacity: 0.5;

  background-size: cover;
  /* background-image: url(/src/images/icons/scissors.svg); */
  width: 16px;
  height: 16px;
}

[type="page-break"]::after {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  display: block;
  padding: 2px 6px;
  border: 1px solid #ccc;
  background-color: #fff;

  content: "PAGE BREAK";
  font-size: 12px;
  color: #000;
  font-weight: 600;
}

.selected[type="page-break"] {
  border-color: var(--editor-color-primary, #4766cb);
}

.selected[type="page-break"]::before {
  opacity: 1;
}

.markdown a {
  @apply cursor-pointer font-medium underline text-tertiary;
}
.markdown h1 {
  @apply scroll-m-20 mt-0 md:text-4xl text-3xl md:font-extrabold font-bold tracking-tight lg:text-5xl;
}
.markdown iframe {
  @apply md:!aspect-video !w-full;
}
.markdown h2 {
  @apply mt-8 scroll-m-20 border-b pb-2 md:text-3xl text-2xl font-semibold tracking-tight;
}
.markdown h3 {
  @apply mt-8 scroll-m-20 md:text-2xl text-xl font-semibold tracking-tight;
}
.markdown h4 {
  @apply scroll-m-20 md:text-xl text-base font-semibold tracking-tight;
}
.markdown p {
  @apply leading-7 [&:not(:first-child)]:mt-6 text-pretty;
}
.markdown div {
  @apply leading-7 [&:not(:first-child)]:mt-6 text-pretty;
}
.markdown blockquote {
  @apply mt-6 border-l-2 pl-6 italic;
}
.markdown ul {
  @apply my-6 ml-6 list-disc [&>li]:mt-2;
}
.markdown code {
  @apply relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold;
}
.markdown table {
  @apply w-full;
}
.markdown tr {
  @apply m-0 border-t p-0;
}
.markdown th {
  @apply border !bg-sub px-4 py-2 text-left font-bold text-font [&[align=center]]:text-center [&[align=right]]:text-right;
}
.markdown td {
  @apply border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right;
}

.markdown ol {
  @apply my-6 ml-6 list-decimal [&>li]:mt-2;
}

.cropper-container {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  touch-action: none;
}

.cropper-grid {
  @apply absolute top-0 left-0 bottom-0 right-0 pointer-events-none;
}

.cropper-grid::before,
.cropper-grid::after {
  content: "";
  position: absolute;
  opacity: 0.5;
  background-color: #fff;
}

.cropper-grid::before {
  @apply left-[33.33%] h-[1px] w-full;
}

.cropper-grid::after {
  @apply top-[33.33%] h-[1px] w-full;
}

.cropper-grid-h1 {
  @apply absolute top-[33.33%] w-full h-[1px] bg-[#ffffff80];
}

.cropper-grid-h2 {
  @apply absolute top-[66.66%] w-full h-[1px] bg-[#ffffff80];
}

.cropper-grid-v1 {
  position: absolute;
  left: 33.33%;
  height: 100%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.5);
}

.cropper-grid-v2 {
  @apply absolute left-[66.66%] h-full w-[1px] bg-[#ffffff80];
}

.cropper-handle {
  @apply absolute size-5 border-2 border-white bg-dark shadow-lg -translate-x-1/2 -translate-y-1/2 rounded-full;
}

.tight.markdown > * {
  @apply m-1 !whitespace-normal [&:not(:first-child)]:mt-1;
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent;
  }
}
