import { ChevronUp } from "lucide-react";
import { useEffect, useState } from "react";
import { CAN_USE_DOM, cn } from "../lib/utils";
import { Button } from "./button";

export const ScrollToTop = () => {
  const [scrolly, setScrolly] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (typeof window === "undefined") return;
      setScrolly(window.scrollY);
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleScrollToTop = () => {
    if (!CAN_USE_DOM) return;
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };
  return (
    <Button
      style={{
        filter: "drop-shadow(0px 0px 15px rgba(255,255,255,0.25))",
      }}
      className={cn(
        "fixed bottom-4 right-4 z-50 transition-all duration-300 ease-in-out size-[52px] rounded-full border-2 border-[#E4E4E4] bg-gradient-3",
        scrolly < 300
          ? "opacity-0 translate-x-10"
          : "opacity-100 translate-x-0",
      )}
      onClick={handleScrollToTop}>
      <ChevronUp className="text-font size-3" />
    </Button>
  );
};
