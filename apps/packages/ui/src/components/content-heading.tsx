import { BoxIcon } from "./icons/box-icon";

interface Props {
  className?: string;
  title: string;
  subTitle: string;
  titleClassName?: string;
}

export const ContentHeading = ({
  title,
  subTitle,
  className,
  titleClassName,
}: Props) => {
  return (
    <div className={className}>
      <h3 className="text-primary sm:text-[40px] text-[24px] font-bold font-mplus capitalize">
        {title}
      </h3>
      <div className="flex text-tertiary items-center gap-x-3">
        <BoxIcon />
        <p className="font-medium sm:text-xl text-sm font-montserrat mb-1">
          {subTitle}
        </p>
      </div>
    </div>
  );
};
