import { useLanguage } from "@vtuber/language/hooks";
import { ChevronDown } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom/client";
import { cn } from "../lib/utils";
import { MediaModal } from "./media-modal";

interface MarkdownProps {
  markdown?: string;
  className?: string;
  isOpen?: any;
  maxLines?: number;
  showToggle?: boolean;
  buttonClassName?: string;
}

function convertStyleStringToObject(styleString: string): React.CSSProperties {
  if (!styleString) return {};
  return styleString
    .split(";")
    .filter((style) => style.trim())
    .reduce<React.CSSProperties>((acc, style) => {
      const parts = style.split(":").map((str) => str.trim());
      if (parts.length !== 2) return acc;
      const [property, value] = parts;
      if (!property) return acc;
      const camelProperty = property.replace(/-([a-z])/g, (g) =>
        g[1] ? g[1].toUpperCase() : "",
      );
      return { ...acc, [camelProperty]: value };
    }, {});
}

export const MarkDown = ({
  markdown,
  className,
  isOpen,
  maxLines = 3,
  showToggle = false,
  buttonClassName,
}: MarkdownProps) => {
  const { getText } = useLanguage();
  const containerRef = useRef<HTMLDivElement>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [shouldShowToggle, setShouldShowToggle] = useState(false);

  useEffect(() => {
    if (containerRef.current) {
      const links = containerRef.current.querySelectorAll("a");
      links.forEach((link) => {
        link.setAttribute("target", "_blank");
        link.setAttribute("rel", "noopener noreferrer");
      });

      const images = containerRef.current.querySelectorAll("img");
      images.forEach((img) => {
        const src = img.getAttribute("src");
        const alt = img.getAttribute("alt") || "";
        const className = img.getAttribute("class") || "";
        const style = img.getAttribute("style");
        if (src) {
          const mediaModal = document.createElement("div");
          const styleObj = convertStyleStringToObject(style || "");
          const root = document.createElement("div");
          ReactDOM.createRoot(root).render(
            <MediaModal
              currentSrc={src}
              alt={alt}
              className={className}>
              <img
                src={src}
                alt={alt}
                className={className}
                style={styleObj}
              />
            </MediaModal>,
          );
          mediaModal.appendChild(root);
          img.parentNode?.replaceChild(mediaModal, img);
        }
      });

      if (showToggle) {
        const element = containerRef.current;
        const computedStyle = window.getComputedStyle(element);
        const lineHeight = parseFloat(computedStyle.lineHeight);
        const maxHeight = lineHeight * maxLines;

        setShouldShowToggle(element.scrollHeight > maxHeight);
      }
    }
  }, [markdown, isOpen, maxLines, showToggle]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const getContainerStyle = (): React.CSSProperties => {
    if (!showToggle || !shouldShowToggle || isExpanded) {
      return {};
    }

    const element = containerRef.current;
    if (!element) return {};

    const computedStyle = window.getComputedStyle(element);
    const lineHeight = parseFloat(computedStyle.lineHeight);
    const maxHeight = lineHeight * maxLines;

    return {
      maxHeight: `${maxHeight}px`,
      overflow: "hidden",
      position: "relative",
    };
  };

  return (
    <div className="relative w-full max-w-full min-w-0">
      <div
        ref={containerRef}
        dangerouslySetInnerHTML={{ __html: markdown || "" }}
        className={cn(
          "markdown w-full max-w-full min-w-0 break-words [&_*]:break-words [&_*]:max-w-full [&_*]:min-w-0 [&_pre]:overflow-x-auto [&_code]:break-all",
          className,
        )}
        style={getContainerStyle()}
      />
      {showToggle && shouldShowToggle && (
        <button
          onClick={toggleExpanded}
          className={cn(
            "font-bold text-primary capitalize text-sm flex justify-center items-center w-full mt-4 gap-x-4",
            buttonClassName,
          )}
          type="button">
          <p>{!isExpanded ? getText("view_more") : getText("see_less")}</p>
          <ChevronDown
            className={cn(
              "transition-transform duration-200 size-[14px]",
              !isExpanded ? "rotate-0" : "rotate-180",
            )}
          />
        </button>
      )}
    </div>
  );
};
