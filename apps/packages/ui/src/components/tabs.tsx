"use client";

import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cva, type VariantProps } from "class-variance-authority";
import { motion } from "motion/react";
import * as React from "react";
import { useForwardRef } from "../hooks/use-forward-ref";
import { cn } from "../lib/utils";

const tabListVariants = cva("w-fit flex gap-3 items-center", {
  variants: {
    variant: {
      default:
        "backdrop-blur-xl border-white/10 bg-white/5 border rounded-2xl p-[3px]",
      indicator: "",
      gradient: "",
      material:
        "bg-background gap-0 items-end border-b-[#656565] border-b-2 w-full",
      outline: "h-auto bg-transparent gap-2",
      golden: "gap-2",
      subtle: "border-font border divide-x divide-font",
      underlined: "border-b border-b-gray01",
      rounded: "",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

const tabContentVariants = cva("", {
  variants: {
    variant: {
      default: "py-12 px-8",
      indicator: "mt-20",
      material: "bg-sub",
      outline: "bg-background",
      golden: "",
      gradient: "bg-gradient-to-b to-[#322A4D] from-[#2F4355]",
      subtle: "",
      underlined: "",
      rounded: "",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

const tabsTriggerVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap transition-all group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 font-medium relative h-full",
  {
    variants: {
      variant: {
        default:
          "data-[state=active]:text-white text-gray-400 rounded-2xl transition-all duration-300 p-3",
        gradient:
          "data-[state=active]:text-white bg-sub data-[state=active]:bg-gradient-to-b from-[#322A4D] to-[#2F4355] border-t border-t-[#2C2A37] rounded-t-sm sm:flex-none flex-1 sm:min-w-[240px] px-3 py-1 ",
        indicator:
          "rounded-xs py-2 min-w-[230px] bg-[#FFE2BB] text-black data-[state=active]:bg-background border border-[#FFE2BB] data-[state=active]:text-[#FFE2BB]",
        material: "py-4 px-2 data-[state=active]:text-tertiary text-gray01",
        outline:
          "py-[10px] px-8 sm:min-w-[250px] rounded-[3px] font-bold font-medium text-xl text-sm border border-font",
        golden:
          "bg-[#FFE2BB] rounded-[6px] border border-[#FFE2BB] text-sub py-2 min-h-[70px] flex-1 data-[state=active]:bg-sub data-[state=active]:text-[#FFE2BB] data-[state=active]:drop-shadow-gray",
        subtle:
          "bg-transparent hover:bg-white/10 text-font data-[state=active]:text-sub",
        underlined:
          "data-[state=active]:text-tertiary text-font font-medium data-[state=active]:font-bold",
        rounded:
          "rounded-full bg-[#2C2A37] text-white data-[state=active]:text-background overflow-hidden",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

const TabsContext = React.createContext<
  VariantProps<typeof tabsTriggerVariants>
>({
  variant: "default",
});

interface TabsProps
  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root>,
    VariantProps<typeof tabsTriggerVariants> {}

const Tabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  TabsProps
>(({ variant = "default", ...props }, ref) => (
  <TabsContext.Provider value={{ variant }}>
    <TabsPrimitive.Root
      ref={ref}
      {...props}
    />
  </TabsContext.Provider>
));
Tabs.displayName = TabsPrimitive.Root.displayName;

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => {
  const { variant } = React.useContext(TabsContext);
  return (
    <TabsPrimitive.List
      ref={ref}
      className={cn(tabListVariants({ variant }), className)}
      {...props}
    />
  );
});
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, children, ...props }, ref) => {
  const { variant } = React.useContext(TabsContext);
  const tabRef = useForwardRef(ref);
  const [active, setActive] = React.useState(false);

  React.useEffect(() => {
    const observer = new MutationObserver((mutationList) => {
      mutationList.forEach((m) => {
        if (m.type == "attributes" && m.attributeName == "data-state") {
          setActive(
            (m.target as HTMLElement | undefined)?.dataset?.state == "active",
          );
        }
      });
    });

    if (tabRef?.current) {
      setActive(tabRef.current.dataset.state == "active");
      observer.observe(tabRef.current, {
        attributes: true,
        attributeFilter: ["data-state"],
      });
    }
    return () => {
      observer.disconnect();
    };
  }, [tabRef?.current]);

  return (
    <TabsPrimitive.Trigger
      ref={tabRef}
      className={cn(tabsTriggerVariants({ variant, className }))}
      {...props}>
      <div className="relative flex items-center gap-2 z-20"> {children}</div>
      {variant === "indicator" && active && (
        <motion.div
          key={"indicator"}
          layoutId="indicator"
          className="absolute bottom-0 z-20 left-[47%] -translate-x-1/2 top-[33px]">
          <div className="border-b border-r border-[#FFE2BB] bg-background size-4 rotate-45 z-10" />
        </motion.div>
      )}
      {variant === "material" && active && (
        <motion.div
          key={"material"}
          layoutId="material"
          className="absolute inset-0 bg-sub rounded-t-10">
          <div className="border-b-2 border-b-tertiary absolute -bottom-0.5 w-full" />
        </motion.div>
      )}
      {variant === "underlined" && active && (
        <div
          key={"underlined"}
          // layoutId="underlined"
          className="absolute inset-0">
          <div className="border-b-2 border-b-tertiary absolute -bottom-0.5 w-full" />
        </div>
      )}
      {variant === "outline" && active && (
        <motion.div
          key={"outline"}
          layoutId="outline"
          className="absolute inset-0 -z-10 bg-gradient-5"
        />
      )}
      {variant === "default" && active && (
        <motion.div
          key={"default"}
          layoutId="default"
          className="absolute inset-0 -z-10 bg-white/10 rounded-2xl"
        />
      )}
      {variant === "subtle" && active && (
        <motion.div
          key={"subtle"}
          layoutId="subtle"
          className="absolute inset-0 z-10 bg-font"
        />
      )}
      {variant === "rounded" && active && (
        <motion.div
          key={"rounded"}
          initial={{ scaleY: 0 }}
          animate={{ scaleY: 1 }}
          exit={{ scaleY: 0 }}
          className="absolute inset-0 origin-bottom bottom-0 z-10 bg-white rounded-full"
        />
      )}
    </TabsPrimitive.Trigger>
  );
});
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => {
  const { variant } = React.useContext(TabsContext);
  return (
    <TabsPrimitive.Content
      ref={ref}
      className={cn(tabContentVariants({ variant, className }))}
      {...props}
    />
  );
});
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsContent, TabsList, TabsTrigger };
