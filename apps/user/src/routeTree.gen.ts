/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TeaserRouteImport } from './routes/teaser'
import { Route as ProtectedRouteImport } from './routes/_protected'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AppRouteImport } from './routes/_app'
import { Route as AppIndexRouteImport } from './routes/_app/index'
import { Route as ProtectedSettingRouteImport } from './routes/_protected/setting'
import { Route as ProtectedPaymentRouteImport } from './routes/_protected/payment'
import { Route as ProtectedNotificationsRouteImport } from './routes/_protected/notifications'
import { Route as ProtectedMyPointsRouteImport } from './routes/_protected/my-points'
import { Route as ProtectedMyPageRouteImport } from './routes/_protected/my-page'
import { Route as ProtectedDeliveryAddressRouteImport } from './routes/_protected/delivery-address'
import { Route as ProtectedCancellationSuccessRouteImport } from './routes/_protected/cancellation-success'
import { Route as ProtectedBillingRouteImport } from './routes/_protected/billing'
import { Route as AuthWelcomeRouteImport } from './routes/_auth/welcome'
import { Route as AuthVerifyEmailRouteImport } from './routes/_auth/verify-email'
import { Route as AuthResetPasswordRouteImport } from './routes/_auth/reset-password'
import { Route as AuthRegisterRouteImport } from './routes/_auth/register'
import { Route as AuthOauthCompleteRouteImport } from './routes/_auth/oauth-complete'
import { Route as AuthLoginRouteImport } from './routes/_auth/login'
import { Route as AuthForgotPasswordRouteImport } from './routes/_auth/forgot-password'
import { Route as AppTransactionActRouteImport } from './routes/_app/transaction-act'
import { Route as AppTermsRouteImport } from './routes/_app/terms'
import { Route as AppSupportRouteImport } from './routes/_app/support'
import { Route as AppSiteMapRouteImport } from './routes/_app/site-map'
import { Route as AppSearchRouteImport } from './routes/_app/search'
import { Route as AppPrivacyRouteImport } from './routes/_app/privacy'
import { Route as AppOperatingCompanyRouteImport } from './routes/_app/operating-company'
import { Route as AppFaqRouteImport } from './routes/_app/faq'
import { Route as AppCommunityGuidelinesRouteImport } from './routes/_app/community-guidelines'
import { Route as AppVtuberIndexRouteImport } from './routes/_app/vtuber/index'
import { Route as AppEventIndexRouteImport } from './routes/_app/event/index'
import { Route as AppContactIndexRouteImport } from './routes/_app/contact/index'
import { Route as AppCampaignIndexRouteImport } from './routes/_app/campaign/index'
import { Route as ProtectedEmailVerifyRouteImport } from './routes/_protected/email/verify'
import { Route as AppEmailVerifyNewEmailRouteImport } from './routes/_app/email/verify-new-email'
import { Route as AppEmailSuccessRouteImport } from './routes/_app/email/success'
import { Route as AppEmailNewEmailRouteImport } from './routes/_app/email/new-email'
import { Route as AppContactThanksRouteImport } from './routes/_app/contact/thanks'
import { Route as AppEventIdRouteRouteImport } from './routes/_app/event/$id/route'
import { Route as AppVtuberIdIndexRouteImport } from './routes/_app/vtuber/$id/index'
import { Route as AppEventIdIndexRouteImport } from './routes/_app/event/$id/index'
import { Route as AppCampaignIdIndexRouteImport } from './routes/_app/campaign/$id/index'
import { Route as PhotoVtuberPostIdRouteImport } from './routes/_photo/vtuber.post.$id'
import { Route as AppEventIdRewardsRouteImport } from './routes/_app/event/$id/rewards'
import { Route as AppEventIdParticipantsRouteImport } from './routes/_app/event/$id/participants'
import { Route as AppCampaignIdContactRouteImport } from './routes/_app/campaign/$id/contact'

const TeaserRoute = TeaserRouteImport.update({
  id: '/teaser',
  path: '/teaser',
  getParentRoute: () => rootRouteImport,
} as any)
const ProtectedRoute = ProtectedRouteImport.update({
  id: '/_protected',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AppRoute = AppRouteImport.update({
  id: '/_app',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppRoute,
} as any)
const ProtectedSettingRoute = ProtectedSettingRouteImport.update({
  id: '/setting',
  path: '/setting',
  getParentRoute: () => ProtectedRoute,
} as any)
const ProtectedPaymentRoute = ProtectedPaymentRouteImport.update({
  id: '/payment',
  path: '/payment',
  getParentRoute: () => ProtectedRoute,
} as any)
const ProtectedNotificationsRoute = ProtectedNotificationsRouteImport.update({
  id: '/notifications',
  path: '/notifications',
  getParentRoute: () => ProtectedRoute,
} as any)
const ProtectedMyPointsRoute = ProtectedMyPointsRouteImport.update({
  id: '/my-points',
  path: '/my-points',
  getParentRoute: () => ProtectedRoute,
} as any)
const ProtectedMyPageRoute = ProtectedMyPageRouteImport.update({
  id: '/my-page',
  path: '/my-page',
  getParentRoute: () => ProtectedRoute,
} as any)
const ProtectedDeliveryAddressRoute =
  ProtectedDeliveryAddressRouteImport.update({
    id: '/delivery-address',
    path: '/delivery-address',
    getParentRoute: () => ProtectedRoute,
  } as any)
const ProtectedCancellationSuccessRoute =
  ProtectedCancellationSuccessRouteImport.update({
    id: '/cancellation-success',
    path: '/cancellation-success',
    getParentRoute: () => ProtectedRoute,
  } as any)
const ProtectedBillingRoute = ProtectedBillingRouteImport.update({
  id: '/billing',
  path: '/billing',
  getParentRoute: () => ProtectedRoute,
} as any)
const AuthWelcomeRoute = AuthWelcomeRouteImport.update({
  id: '/welcome',
  path: '/welcome',
  getParentRoute: () => AuthRoute,
} as any)
const AuthVerifyEmailRoute = AuthVerifyEmailRouteImport.update({
  id: '/verify-email',
  path: '/verify-email',
  getParentRoute: () => AuthRoute,
} as any)
const AuthResetPasswordRoute = AuthResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => AuthRoute,
} as any)
const AuthRegisterRoute = AuthRegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => AuthRoute,
} as any)
const AuthOauthCompleteRoute = AuthOauthCompleteRouteImport.update({
  id: '/oauth-complete',
  path: '/oauth-complete',
  getParentRoute: () => AuthRoute,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)
const AuthForgotPasswordRoute = AuthForgotPasswordRouteImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => AuthRoute,
} as any)
const AppTransactionActRoute = AppTransactionActRouteImport.update({
  id: '/transaction-act',
  path: '/transaction-act',
  getParentRoute: () => AppRoute,
} as any)
const AppTermsRoute = AppTermsRouteImport.update({
  id: '/terms',
  path: '/terms',
  getParentRoute: () => AppRoute,
} as any)
const AppSupportRoute = AppSupportRouteImport.update({
  id: '/support',
  path: '/support',
  getParentRoute: () => AppRoute,
} as any)
const AppSiteMapRoute = AppSiteMapRouteImport.update({
  id: '/site-map',
  path: '/site-map',
  getParentRoute: () => AppRoute,
} as any)
const AppSearchRoute = AppSearchRouteImport.update({
  id: '/search',
  path: '/search',
  getParentRoute: () => AppRoute,
} as any)
const AppPrivacyRoute = AppPrivacyRouteImport.update({
  id: '/privacy',
  path: '/privacy',
  getParentRoute: () => AppRoute,
} as any)
const AppOperatingCompanyRoute = AppOperatingCompanyRouteImport.update({
  id: '/operating-company',
  path: '/operating-company',
  getParentRoute: () => AppRoute,
} as any)
const AppFaqRoute = AppFaqRouteImport.update({
  id: '/faq',
  path: '/faq',
  getParentRoute: () => AppRoute,
} as any)
const AppCommunityGuidelinesRoute = AppCommunityGuidelinesRouteImport.update({
  id: '/community-guidelines',
  path: '/community-guidelines',
  getParentRoute: () => AppRoute,
} as any)
const AppVtuberIndexRoute = AppVtuberIndexRouteImport.update({
  id: '/vtuber/',
  path: '/vtuber/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventIndexRoute = AppEventIndexRouteImport.update({
  id: '/event/',
  path: '/event/',
  getParentRoute: () => AppRoute,
} as any)
const AppContactIndexRoute = AppContactIndexRouteImport.update({
  id: '/contact/',
  path: '/contact/',
  getParentRoute: () => AppRoute,
} as any)
const AppCampaignIndexRoute = AppCampaignIndexRouteImport.update({
  id: '/campaign/',
  path: '/campaign/',
  getParentRoute: () => AppRoute,
} as any)
const ProtectedEmailVerifyRoute = ProtectedEmailVerifyRouteImport.update({
  id: '/email/verify',
  path: '/email/verify',
  getParentRoute: () => ProtectedRoute,
} as any)
const AppEmailVerifyNewEmailRoute = AppEmailVerifyNewEmailRouteImport.update({
  id: '/email/verify-new-email',
  path: '/email/verify-new-email',
  getParentRoute: () => AppRoute,
} as any)
const AppEmailSuccessRoute = AppEmailSuccessRouteImport.update({
  id: '/email/success',
  path: '/email/success',
  getParentRoute: () => AppRoute,
} as any)
const AppEmailNewEmailRoute = AppEmailNewEmailRouteImport.update({
  id: '/email/new-email',
  path: '/email/new-email',
  getParentRoute: () => AppRoute,
} as any)
const AppContactThanksRoute = AppContactThanksRouteImport.update({
  id: '/contact/thanks',
  path: '/contact/thanks',
  getParentRoute: () => AppRoute,
} as any)
const AppEventIdRouteRoute = AppEventIdRouteRouteImport.update({
  id: '/event/$id',
  path: '/event/$id',
  getParentRoute: () => AppRoute,
} as any)
const AppVtuberIdIndexRoute = AppVtuberIdIndexRouteImport.update({
  id: '/vtuber/$id/',
  path: '/vtuber/$id/',
  getParentRoute: () => AppRoute,
} as any)
const AppEventIdIndexRoute = AppEventIdIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppEventIdRouteRoute,
} as any)
const AppCampaignIdIndexRoute = AppCampaignIdIndexRouteImport.update({
  id: '/campaign/$id/',
  path: '/campaign/$id/',
  getParentRoute: () => AppRoute,
} as any)
const PhotoVtuberPostIdRoute = PhotoVtuberPostIdRouteImport.update({
  id: '/_photo/vtuber/post/$id',
  path: '/vtuber/post/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const AppEventIdRewardsRoute = AppEventIdRewardsRouteImport.update({
  id: '/rewards',
  path: '/rewards',
  getParentRoute: () => AppEventIdRouteRoute,
} as any)
const AppEventIdParticipantsRoute = AppEventIdParticipantsRouteImport.update({
  id: '/participants',
  path: '/participants',
  getParentRoute: () => AppEventIdRouteRoute,
} as any)
const AppCampaignIdContactRoute = AppCampaignIdContactRouteImport.update({
  id: '/campaign/$id/contact',
  path: '/campaign/$id/contact',
  getParentRoute: () => AppRoute,
} as any)

export interface FileRoutesByFullPath {
  '/teaser': typeof TeaserRoute
  '/community-guidelines': typeof AppCommunityGuidelinesRoute
  '/faq': typeof AppFaqRoute
  '/operating-company': typeof AppOperatingCompanyRoute
  '/privacy': typeof AppPrivacyRoute
  '/search': typeof AppSearchRoute
  '/site-map': typeof AppSiteMapRoute
  '/support': typeof AppSupportRoute
  '/terms': typeof AppTermsRoute
  '/transaction-act': typeof AppTransactionActRoute
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/oauth-complete': typeof AuthOauthCompleteRoute
  '/register': typeof AuthRegisterRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/verify-email': typeof AuthVerifyEmailRoute
  '/welcome': typeof AuthWelcomeRoute
  '/billing': typeof ProtectedBillingRoute
  '/cancellation-success': typeof ProtectedCancellationSuccessRoute
  '/delivery-address': typeof ProtectedDeliveryAddressRoute
  '/my-page': typeof ProtectedMyPageRoute
  '/my-points': typeof ProtectedMyPointsRoute
  '/notifications': typeof ProtectedNotificationsRoute
  '/payment': typeof ProtectedPaymentRoute
  '/setting': typeof ProtectedSettingRoute
  '/': typeof AppIndexRoute
  '/event/$id': typeof AppEventIdRouteRouteWithChildren
  '/contact/thanks': typeof AppContactThanksRoute
  '/email/new-email': typeof AppEmailNewEmailRoute
  '/email/success': typeof AppEmailSuccessRoute
  '/email/verify-new-email': typeof AppEmailVerifyNewEmailRoute
  '/email/verify': typeof ProtectedEmailVerifyRoute
  '/campaign': typeof AppCampaignIndexRoute
  '/contact': typeof AppContactIndexRoute
  '/event': typeof AppEventIndexRoute
  '/vtuber': typeof AppVtuberIndexRoute
  '/campaign/$id/contact': typeof AppCampaignIdContactRoute
  '/event/$id/participants': typeof AppEventIdParticipantsRoute
  '/event/$id/rewards': typeof AppEventIdRewardsRoute
  '/vtuber/post/$id': typeof PhotoVtuberPostIdRoute
  '/campaign/$id': typeof AppCampaignIdIndexRoute
  '/event/$id/': typeof AppEventIdIndexRoute
  '/vtuber/$id': typeof AppVtuberIdIndexRoute
}
export interface FileRoutesByTo {
  '/teaser': typeof TeaserRoute
  '/community-guidelines': typeof AppCommunityGuidelinesRoute
  '/faq': typeof AppFaqRoute
  '/operating-company': typeof AppOperatingCompanyRoute
  '/privacy': typeof AppPrivacyRoute
  '/search': typeof AppSearchRoute
  '/site-map': typeof AppSiteMapRoute
  '/support': typeof AppSupportRoute
  '/terms': typeof AppTermsRoute
  '/transaction-act': typeof AppTransactionActRoute
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/oauth-complete': typeof AuthOauthCompleteRoute
  '/register': typeof AuthRegisterRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/verify-email': typeof AuthVerifyEmailRoute
  '/welcome': typeof AuthWelcomeRoute
  '/billing': typeof ProtectedBillingRoute
  '/cancellation-success': typeof ProtectedCancellationSuccessRoute
  '/delivery-address': typeof ProtectedDeliveryAddressRoute
  '/my-page': typeof ProtectedMyPageRoute
  '/my-points': typeof ProtectedMyPointsRoute
  '/notifications': typeof ProtectedNotificationsRoute
  '/payment': typeof ProtectedPaymentRoute
  '/setting': typeof ProtectedSettingRoute
  '/': typeof AppIndexRoute
  '/contact/thanks': typeof AppContactThanksRoute
  '/email/new-email': typeof AppEmailNewEmailRoute
  '/email/success': typeof AppEmailSuccessRoute
  '/email/verify-new-email': typeof AppEmailVerifyNewEmailRoute
  '/email/verify': typeof ProtectedEmailVerifyRoute
  '/campaign': typeof AppCampaignIndexRoute
  '/contact': typeof AppContactIndexRoute
  '/event': typeof AppEventIndexRoute
  '/vtuber': typeof AppVtuberIndexRoute
  '/campaign/$id/contact': typeof AppCampaignIdContactRoute
  '/event/$id/participants': typeof AppEventIdParticipantsRoute
  '/event/$id/rewards': typeof AppEventIdRewardsRoute
  '/vtuber/post/$id': typeof PhotoVtuberPostIdRoute
  '/campaign/$id': typeof AppCampaignIdIndexRoute
  '/event/$id': typeof AppEventIdIndexRoute
  '/vtuber/$id': typeof AppVtuberIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_app': typeof AppRouteWithChildren
  '/_auth': typeof AuthRouteWithChildren
  '/_protected': typeof ProtectedRouteWithChildren
  '/teaser': typeof TeaserRoute
  '/_app/community-guidelines': typeof AppCommunityGuidelinesRoute
  '/_app/faq': typeof AppFaqRoute
  '/_app/operating-company': typeof AppOperatingCompanyRoute
  '/_app/privacy': typeof AppPrivacyRoute
  '/_app/search': typeof AppSearchRoute
  '/_app/site-map': typeof AppSiteMapRoute
  '/_app/support': typeof AppSupportRoute
  '/_app/terms': typeof AppTermsRoute
  '/_app/transaction-act': typeof AppTransactionActRoute
  '/_auth/forgot-password': typeof AuthForgotPasswordRoute
  '/_auth/login': typeof AuthLoginRoute
  '/_auth/oauth-complete': typeof AuthOauthCompleteRoute
  '/_auth/register': typeof AuthRegisterRoute
  '/_auth/reset-password': typeof AuthResetPasswordRoute
  '/_auth/verify-email': typeof AuthVerifyEmailRoute
  '/_auth/welcome': typeof AuthWelcomeRoute
  '/_protected/billing': typeof ProtectedBillingRoute
  '/_protected/cancellation-success': typeof ProtectedCancellationSuccessRoute
  '/_protected/delivery-address': typeof ProtectedDeliveryAddressRoute
  '/_protected/my-page': typeof ProtectedMyPageRoute
  '/_protected/my-points': typeof ProtectedMyPointsRoute
  '/_protected/notifications': typeof ProtectedNotificationsRoute
  '/_protected/payment': typeof ProtectedPaymentRoute
  '/_protected/setting': typeof ProtectedSettingRoute
  '/_app/': typeof AppIndexRoute
  '/_app/event/$id': typeof AppEventIdRouteRouteWithChildren
  '/_app/contact/thanks': typeof AppContactThanksRoute
  '/_app/email/new-email': typeof AppEmailNewEmailRoute
  '/_app/email/success': typeof AppEmailSuccessRoute
  '/_app/email/verify-new-email': typeof AppEmailVerifyNewEmailRoute
  '/_protected/email/verify': typeof ProtectedEmailVerifyRoute
  '/_app/campaign/': typeof AppCampaignIndexRoute
  '/_app/contact/': typeof AppContactIndexRoute
  '/_app/event/': typeof AppEventIndexRoute
  '/_app/vtuber/': typeof AppVtuberIndexRoute
  '/_app/campaign/$id/contact': typeof AppCampaignIdContactRoute
  '/_app/event/$id/participants': typeof AppEventIdParticipantsRoute
  '/_app/event/$id/rewards': typeof AppEventIdRewardsRoute
  '/_photo/vtuber/post/$id': typeof PhotoVtuberPostIdRoute
  '/_app/campaign/$id/': typeof AppCampaignIdIndexRoute
  '/_app/event/$id/': typeof AppEventIdIndexRoute
  '/_app/vtuber/$id/': typeof AppVtuberIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/teaser'
    | '/community-guidelines'
    | '/faq'
    | '/operating-company'
    | '/privacy'
    | '/search'
    | '/site-map'
    | '/support'
    | '/terms'
    | '/transaction-act'
    | '/forgot-password'
    | '/login'
    | '/oauth-complete'
    | '/register'
    | '/reset-password'
    | '/verify-email'
    | '/welcome'
    | '/billing'
    | '/cancellation-success'
    | '/delivery-address'
    | '/my-page'
    | '/my-points'
    | '/notifications'
    | '/payment'
    | '/setting'
    | '/'
    | '/event/$id'
    | '/contact/thanks'
    | '/email/new-email'
    | '/email/success'
    | '/email/verify-new-email'
    | '/email/verify'
    | '/campaign'
    | '/contact'
    | '/event'
    | '/vtuber'
    | '/campaign/$id/contact'
    | '/event/$id/participants'
    | '/event/$id/rewards'
    | '/vtuber/post/$id'
    | '/campaign/$id'
    | '/event/$id/'
    | '/vtuber/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/teaser'
    | '/community-guidelines'
    | '/faq'
    | '/operating-company'
    | '/privacy'
    | '/search'
    | '/site-map'
    | '/support'
    | '/terms'
    | '/transaction-act'
    | '/forgot-password'
    | '/login'
    | '/oauth-complete'
    | '/register'
    | '/reset-password'
    | '/verify-email'
    | '/welcome'
    | '/billing'
    | '/cancellation-success'
    | '/delivery-address'
    | '/my-page'
    | '/my-points'
    | '/notifications'
    | '/payment'
    | '/setting'
    | '/'
    | '/contact/thanks'
    | '/email/new-email'
    | '/email/success'
    | '/email/verify-new-email'
    | '/email/verify'
    | '/campaign'
    | '/contact'
    | '/event'
    | '/vtuber'
    | '/campaign/$id/contact'
    | '/event/$id/participants'
    | '/event/$id/rewards'
    | '/vtuber/post/$id'
    | '/campaign/$id'
    | '/event/$id'
    | '/vtuber/$id'
  id:
    | '__root__'
    | '/_app'
    | '/_auth'
    | '/_protected'
    | '/teaser'
    | '/_app/community-guidelines'
    | '/_app/faq'
    | '/_app/operating-company'
    | '/_app/privacy'
    | '/_app/search'
    | '/_app/site-map'
    | '/_app/support'
    | '/_app/terms'
    | '/_app/transaction-act'
    | '/_auth/forgot-password'
    | '/_auth/login'
    | '/_auth/oauth-complete'
    | '/_auth/register'
    | '/_auth/reset-password'
    | '/_auth/verify-email'
    | '/_auth/welcome'
    | '/_protected/billing'
    | '/_protected/cancellation-success'
    | '/_protected/delivery-address'
    | '/_protected/my-page'
    | '/_protected/my-points'
    | '/_protected/notifications'
    | '/_protected/payment'
    | '/_protected/setting'
    | '/_app/'
    | '/_app/event/$id'
    | '/_app/contact/thanks'
    | '/_app/email/new-email'
    | '/_app/email/success'
    | '/_app/email/verify-new-email'
    | '/_protected/email/verify'
    | '/_app/campaign/'
    | '/_app/contact/'
    | '/_app/event/'
    | '/_app/vtuber/'
    | '/_app/campaign/$id/contact'
    | '/_app/event/$id/participants'
    | '/_app/event/$id/rewards'
    | '/_photo/vtuber/post/$id'
    | '/_app/campaign/$id/'
    | '/_app/event/$id/'
    | '/_app/vtuber/$id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AppRoute: typeof AppRouteWithChildren
  AuthRoute: typeof AuthRouteWithChildren
  ProtectedRoute: typeof ProtectedRouteWithChildren
  TeaserRoute: typeof TeaserRoute
  PhotoVtuberPostIdRoute: typeof PhotoVtuberPostIdRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/teaser': {
      id: '/teaser'
      path: '/teaser'
      fullPath: '/teaser'
      preLoaderRoute: typeof TeaserRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_protected': {
      id: '/_protected'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof ProtectedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app': {
      id: '/_app'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/': {
      id: '/_app/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_protected/setting': {
      id: '/_protected/setting'
      path: '/setting'
      fullPath: '/setting'
      preLoaderRoute: typeof ProtectedSettingRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/payment': {
      id: '/_protected/payment'
      path: '/payment'
      fullPath: '/payment'
      preLoaderRoute: typeof ProtectedPaymentRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/notifications': {
      id: '/_protected/notifications'
      path: '/notifications'
      fullPath: '/notifications'
      preLoaderRoute: typeof ProtectedNotificationsRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/my-points': {
      id: '/_protected/my-points'
      path: '/my-points'
      fullPath: '/my-points'
      preLoaderRoute: typeof ProtectedMyPointsRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/my-page': {
      id: '/_protected/my-page'
      path: '/my-page'
      fullPath: '/my-page'
      preLoaderRoute: typeof ProtectedMyPageRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/delivery-address': {
      id: '/_protected/delivery-address'
      path: '/delivery-address'
      fullPath: '/delivery-address'
      preLoaderRoute: typeof ProtectedDeliveryAddressRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/cancellation-success': {
      id: '/_protected/cancellation-success'
      path: '/cancellation-success'
      fullPath: '/cancellation-success'
      preLoaderRoute: typeof ProtectedCancellationSuccessRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_protected/billing': {
      id: '/_protected/billing'
      path: '/billing'
      fullPath: '/billing'
      preLoaderRoute: typeof ProtectedBillingRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_auth/welcome': {
      id: '/_auth/welcome'
      path: '/welcome'
      fullPath: '/welcome'
      preLoaderRoute: typeof AuthWelcomeRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/verify-email': {
      id: '/_auth/verify-email'
      path: '/verify-email'
      fullPath: '/verify-email'
      preLoaderRoute: typeof AuthVerifyEmailRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/reset-password': {
      id: '/_auth/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof AuthResetPasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/register': {
      id: '/_auth/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof AuthRegisterRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/oauth-complete': {
      id: '/_auth/oauth-complete'
      path: '/oauth-complete'
      fullPath: '/oauth-complete'
      preLoaderRoute: typeof AuthOauthCompleteRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/forgot-password': {
      id: '/_auth/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof AuthForgotPasswordRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_app/transaction-act': {
      id: '/_app/transaction-act'
      path: '/transaction-act'
      fullPath: '/transaction-act'
      preLoaderRoute: typeof AppTransactionActRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/terms': {
      id: '/_app/terms'
      path: '/terms'
      fullPath: '/terms'
      preLoaderRoute: typeof AppTermsRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/support': {
      id: '/_app/support'
      path: '/support'
      fullPath: '/support'
      preLoaderRoute: typeof AppSupportRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/site-map': {
      id: '/_app/site-map'
      path: '/site-map'
      fullPath: '/site-map'
      preLoaderRoute: typeof AppSiteMapRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/search': {
      id: '/_app/search'
      path: '/search'
      fullPath: '/search'
      preLoaderRoute: typeof AppSearchRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/privacy': {
      id: '/_app/privacy'
      path: '/privacy'
      fullPath: '/privacy'
      preLoaderRoute: typeof AppPrivacyRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/operating-company': {
      id: '/_app/operating-company'
      path: '/operating-company'
      fullPath: '/operating-company'
      preLoaderRoute: typeof AppOperatingCompanyRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/faq': {
      id: '/_app/faq'
      path: '/faq'
      fullPath: '/faq'
      preLoaderRoute: typeof AppFaqRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/community-guidelines': {
      id: '/_app/community-guidelines'
      path: '/community-guidelines'
      fullPath: '/community-guidelines'
      preLoaderRoute: typeof AppCommunityGuidelinesRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/vtuber/': {
      id: '/_app/vtuber/'
      path: '/vtuber'
      fullPath: '/vtuber'
      preLoaderRoute: typeof AppVtuberIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/': {
      id: '/_app/event/'
      path: '/event'
      fullPath: '/event'
      preLoaderRoute: typeof AppEventIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/contact/': {
      id: '/_app/contact/'
      path: '/contact'
      fullPath: '/contact'
      preLoaderRoute: typeof AppContactIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/campaign/': {
      id: '/_app/campaign/'
      path: '/campaign'
      fullPath: '/campaign'
      preLoaderRoute: typeof AppCampaignIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_protected/email/verify': {
      id: '/_protected/email/verify'
      path: '/email/verify'
      fullPath: '/email/verify'
      preLoaderRoute: typeof ProtectedEmailVerifyRouteImport
      parentRoute: typeof ProtectedRoute
    }
    '/_app/email/verify-new-email': {
      id: '/_app/email/verify-new-email'
      path: '/email/verify-new-email'
      fullPath: '/email/verify-new-email'
      preLoaderRoute: typeof AppEmailVerifyNewEmailRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/email/success': {
      id: '/_app/email/success'
      path: '/email/success'
      fullPath: '/email/success'
      preLoaderRoute: typeof AppEmailSuccessRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/email/new-email': {
      id: '/_app/email/new-email'
      path: '/email/new-email'
      fullPath: '/email/new-email'
      preLoaderRoute: typeof AppEmailNewEmailRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/contact/thanks': {
      id: '/_app/contact/thanks'
      path: '/contact/thanks'
      fullPath: '/contact/thanks'
      preLoaderRoute: typeof AppContactThanksRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/$id': {
      id: '/_app/event/$id'
      path: '/event/$id'
      fullPath: '/event/$id'
      preLoaderRoute: typeof AppEventIdRouteRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/vtuber/$id/': {
      id: '/_app/vtuber/$id/'
      path: '/vtuber/$id'
      fullPath: '/vtuber/$id'
      preLoaderRoute: typeof AppVtuberIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_app/event/$id/': {
      id: '/_app/event/$id/'
      path: '/'
      fullPath: '/event/$id/'
      preLoaderRoute: typeof AppEventIdIndexRouteImport
      parentRoute: typeof AppEventIdRouteRoute
    }
    '/_app/campaign/$id/': {
      id: '/_app/campaign/$id/'
      path: '/campaign/$id'
      fullPath: '/campaign/$id'
      preLoaderRoute: typeof AppCampaignIdIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/_photo/vtuber/post/$id': {
      id: '/_photo/vtuber/post/$id'
      path: '/vtuber/post/$id'
      fullPath: '/vtuber/post/$id'
      preLoaderRoute: typeof PhotoVtuberPostIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_app/event/$id/rewards': {
      id: '/_app/event/$id/rewards'
      path: '/rewards'
      fullPath: '/event/$id/rewards'
      preLoaderRoute: typeof AppEventIdRewardsRouteImport
      parentRoute: typeof AppEventIdRouteRoute
    }
    '/_app/event/$id/participants': {
      id: '/_app/event/$id/participants'
      path: '/participants'
      fullPath: '/event/$id/participants'
      preLoaderRoute: typeof AppEventIdParticipantsRouteImport
      parentRoute: typeof AppEventIdRouteRoute
    }
    '/_app/campaign/$id/contact': {
      id: '/_app/campaign/$id/contact'
      path: '/campaign/$id/contact'
      fullPath: '/campaign/$id/contact'
      preLoaderRoute: typeof AppCampaignIdContactRouteImport
      parentRoute: typeof AppRoute
    }
  }
}

interface AppEventIdRouteRouteChildren {
  AppEventIdParticipantsRoute: typeof AppEventIdParticipantsRoute
  AppEventIdRewardsRoute: typeof AppEventIdRewardsRoute
  AppEventIdIndexRoute: typeof AppEventIdIndexRoute
}

const AppEventIdRouteRouteChildren: AppEventIdRouteRouteChildren = {
  AppEventIdParticipantsRoute: AppEventIdParticipantsRoute,
  AppEventIdRewardsRoute: AppEventIdRewardsRoute,
  AppEventIdIndexRoute: AppEventIdIndexRoute,
}

const AppEventIdRouteRouteWithChildren = AppEventIdRouteRoute._addFileChildren(
  AppEventIdRouteRouteChildren,
)

interface AppRouteChildren {
  AppCommunityGuidelinesRoute: typeof AppCommunityGuidelinesRoute
  AppFaqRoute: typeof AppFaqRoute
  AppOperatingCompanyRoute: typeof AppOperatingCompanyRoute
  AppPrivacyRoute: typeof AppPrivacyRoute
  AppSearchRoute: typeof AppSearchRoute
  AppSiteMapRoute: typeof AppSiteMapRoute
  AppSupportRoute: typeof AppSupportRoute
  AppTermsRoute: typeof AppTermsRoute
  AppTransactionActRoute: typeof AppTransactionActRoute
  AppIndexRoute: typeof AppIndexRoute
  AppEventIdRouteRoute: typeof AppEventIdRouteRouteWithChildren
  AppContactThanksRoute: typeof AppContactThanksRoute
  AppEmailNewEmailRoute: typeof AppEmailNewEmailRoute
  AppEmailSuccessRoute: typeof AppEmailSuccessRoute
  AppEmailVerifyNewEmailRoute: typeof AppEmailVerifyNewEmailRoute
  AppCampaignIndexRoute: typeof AppCampaignIndexRoute
  AppContactIndexRoute: typeof AppContactIndexRoute
  AppEventIndexRoute: typeof AppEventIndexRoute
  AppVtuberIndexRoute: typeof AppVtuberIndexRoute
  AppCampaignIdContactRoute: typeof AppCampaignIdContactRoute
  AppCampaignIdIndexRoute: typeof AppCampaignIdIndexRoute
  AppVtuberIdIndexRoute: typeof AppVtuberIdIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppCommunityGuidelinesRoute: AppCommunityGuidelinesRoute,
  AppFaqRoute: AppFaqRoute,
  AppOperatingCompanyRoute: AppOperatingCompanyRoute,
  AppPrivacyRoute: AppPrivacyRoute,
  AppSearchRoute: AppSearchRoute,
  AppSiteMapRoute: AppSiteMapRoute,
  AppSupportRoute: AppSupportRoute,
  AppTermsRoute: AppTermsRoute,
  AppTransactionActRoute: AppTransactionActRoute,
  AppIndexRoute: AppIndexRoute,
  AppEventIdRouteRoute: AppEventIdRouteRouteWithChildren,
  AppContactThanksRoute: AppContactThanksRoute,
  AppEmailNewEmailRoute: AppEmailNewEmailRoute,
  AppEmailSuccessRoute: AppEmailSuccessRoute,
  AppEmailVerifyNewEmailRoute: AppEmailVerifyNewEmailRoute,
  AppCampaignIndexRoute: AppCampaignIndexRoute,
  AppContactIndexRoute: AppContactIndexRoute,
  AppEventIndexRoute: AppEventIndexRoute,
  AppVtuberIndexRoute: AppVtuberIndexRoute,
  AppCampaignIdContactRoute: AppCampaignIdContactRoute,
  AppCampaignIdIndexRoute: AppCampaignIdIndexRoute,
  AppVtuberIdIndexRoute: AppVtuberIdIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

interface AuthRouteChildren {
  AuthForgotPasswordRoute: typeof AuthForgotPasswordRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthOauthCompleteRoute: typeof AuthOauthCompleteRoute
  AuthRegisterRoute: typeof AuthRegisterRoute
  AuthResetPasswordRoute: typeof AuthResetPasswordRoute
  AuthVerifyEmailRoute: typeof AuthVerifyEmailRoute
  AuthWelcomeRoute: typeof AuthWelcomeRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthForgotPasswordRoute: AuthForgotPasswordRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthOauthCompleteRoute: AuthOauthCompleteRoute,
  AuthRegisterRoute: AuthRegisterRoute,
  AuthResetPasswordRoute: AuthResetPasswordRoute,
  AuthVerifyEmailRoute: AuthVerifyEmailRoute,
  AuthWelcomeRoute: AuthWelcomeRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

interface ProtectedRouteChildren {
  ProtectedBillingRoute: typeof ProtectedBillingRoute
  ProtectedCancellationSuccessRoute: typeof ProtectedCancellationSuccessRoute
  ProtectedDeliveryAddressRoute: typeof ProtectedDeliveryAddressRoute
  ProtectedMyPageRoute: typeof ProtectedMyPageRoute
  ProtectedMyPointsRoute: typeof ProtectedMyPointsRoute
  ProtectedNotificationsRoute: typeof ProtectedNotificationsRoute
  ProtectedPaymentRoute: typeof ProtectedPaymentRoute
  ProtectedSettingRoute: typeof ProtectedSettingRoute
  ProtectedEmailVerifyRoute: typeof ProtectedEmailVerifyRoute
}

const ProtectedRouteChildren: ProtectedRouteChildren = {
  ProtectedBillingRoute: ProtectedBillingRoute,
  ProtectedCancellationSuccessRoute: ProtectedCancellationSuccessRoute,
  ProtectedDeliveryAddressRoute: ProtectedDeliveryAddressRoute,
  ProtectedMyPageRoute: ProtectedMyPageRoute,
  ProtectedMyPointsRoute: ProtectedMyPointsRoute,
  ProtectedNotificationsRoute: ProtectedNotificationsRoute,
  ProtectedPaymentRoute: ProtectedPaymentRoute,
  ProtectedSettingRoute: ProtectedSettingRoute,
  ProtectedEmailVerifyRoute: ProtectedEmailVerifyRoute,
}

const ProtectedRouteWithChildren = ProtectedRoute._addFileChildren(
  ProtectedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AppRoute: AppRouteWithChildren,
  AuthRoute: AuthRouteWithChildren,
  ProtectedRoute: ProtectedRouteWithChildren,
  TeaserRoute: TeaserRoute,
  PhotoVtuberPostIdRoute: PhotoVtuberPostIdRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
