import { Link } from "@tanstack/react-router";
import { VtuberProfile } from "@vtuber/services/vtubers";
import { Image } from "@vtuber/ui/components/image";
import { cn } from "@vtuber/ui/lib/utils";

interface Props {
  className?: string;
  vtuber: VtuberProfile;
}

export const VtuberCard = ({ className, vtuber }: Props) => {
  return (
    <Link
      to="/vtuber/$id"
      params={{
        id: vtuber.username,
      }}
      className={cn("space-y-3 block", className)}>
      <div className="relative w-full h-full rounded-2xl overflow-hidden">
        <Image
          src={vtuber.image}
          alt={vtuber.displayName}
          className="size-full aspect-video"
        />
        <p className="absolute bottom-2 left-2 bg-black/60 text-white text-sm px-2 py-1 rounded">
          {vtuber.displayName}
        </p>
      </div>
    </Link>
  );
};
