import { Link, useLocation } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { LanguageSelector } from "@vtuber/ui/components/language-selector";
import { Logo } from "@vtuber/ui/components/logo";
import { CAN_USE_DOM, cn } from "@vtuber/ui/lib/utils";
import { SearchIcon } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useEffect, useState } from "react";
import { HeaderLinks } from "./header-links";
import { MobileSidebar } from "./mobile-sidebar";
import { NotificationDropdown } from "./notification/notification-dropdown";
import { PointsBadge } from "./points-badge";
import { SearchBar } from "./search-bar";
import { UserDropDown } from "./user-dropdown";

export const Header = ({ className }: { className?: string }) => {
  const { pathname } = useLocation();
  const [showSearch, setShowSearch] = useState(false);
  const { session } = useAuth();
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const handleScroll = (e: Event) => {
      const scrollPosition = (e.target as Document).scrollingElement?.scrollTop;
      setScrollPosition(scrollPosition || 0);
    };

    document.addEventListener("scroll", handleScroll);

    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={cn(
        "sm:px-10 px-4 lg:bg-transparent lg:shadow-none shadow-lg",
        pathname === "/" && scrollPosition > 831
          ? "bg-background/30 backdrop-blur w-full top-0 z-50 fixed animate-slide-in-down"
          : "absolute top-0 w-full z-50",
        pathname !== "/" &&
          "fixed top-0 z-50 bg-background/30 backdrop-blur-[8px]",
        className,
      )}>
      <AnimatePresence mode="wait">
        {showSearch ? (
          <>
            <motion.button
              key="search-overlay-button"
              onClick={() => {
                setShowSearch(false);
                if (CAN_USE_DOM) {
                  window.document.body.style.overflow = "auto";
                }
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute h-screen w-full top-0 left-0 bg-black/50 -z-10 cursor-default"
            />

            <motion.div
              key="search-overlay-bg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 bg-sub -z-10"
            />
          </>
        ) : null}
      </AnimatePresence>

      <div className="w-full">
        <div className="w-full flex items-center h-20 justify-between">
          <div className="flex items-center lg:gap-x-6">
            <Link to="/">
              <Logo className="sm:w-[112px] sm:h-9 w-[96px] h-[30px]" />
            </Link>
            <button
              onClick={() => {
                setShowSearch((prev) => !prev);
              }}
              className={cn(
                "px-4 py-2 rounded-[40px] border border-white hidden xl:block",
                showSearch ? "bg-primary" : "bg-transparent",
              )}>
              <SearchIcon />
            </button>
          </div>

          <div className="flex xl:hidden items-center sm:gap-6 gap-3">
            <div className="md:pr-8 pr-2">
              <UserDropDown />
            </div>
            <button
              onClick={() => {
                setShowSearch((prev) => !prev);
              }}
              className={cn(
                "px-4 py-2 rounded-[40px] border border-white",
                showSearch ? "bg-primary" : "bg-gradient-3",
              )}>
              <SearchIcon />
            </button>
            <MobileSidebar />
          </div>

          <section className="items-center gap-x-[22px] xl:flex hidden">
            <HeaderLinks
              className={scrollPosition > 831 ? "text-white" : "text-font"}
            />
            {session && <PointsBadge />}
            <LanguageSelector className="rounded-full size-8 lg:flex hidden items-center justify-center" />
            {session && <NotificationDropdown />}
            <UserDropDown />
          </section>
        </div>

        <AnimatePresence mode="wait">
          {showSearch ? (
            <motion.div
              initial={{
                opacity: 0,
                y: -10,
              }}
              animate={{
                opacity: 1,
                y: 0,
              }}
              exit={{
                opacity: 0,
                y: -10,
              }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}>
              <SearchBar
                categories={["#3Dモデル制作", "#衣装制作"]}
                className="py-10 md:max-w-[766px] mx-auto"
                buttonWrapperClassName="md:max-w-full"
                key="header-searchbar"
                setShowSearch={setShowSearch}
              />
            </motion.div>
          ) : null}
        </AnimatePresence>
      </div>
    </header>
  );
};
