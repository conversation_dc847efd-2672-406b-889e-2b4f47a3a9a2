import { useSearch } from "@tanstack/react-router";
import { GetCampaignById } from "@vtuber/services/campaigns";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useScrollInToView } from "@vtuber/ui/hooks/use-scroll-intoview";
import { Suspense, useEffect, useState } from "react";

import { useLanguage } from "@vtuber/language/hooks";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { ArrowDown } from "lucide-react";
import { PostCardSkeleton } from "../skeletons/post-card-skeleton";
import { PostList } from "../vtuber/post-lists";
import { CampaignSupporters } from "./campaign-supporters";
import { CampaignVariantsList } from "./campaign-variants-list";

type Props = {
  campaign: GetCampaignById;
  canSubscribe: boolean;
  ref: React.RefObject<HTMLDivElement | null>;
};

export const CampaignOverviewTabs = ({
  campaign,
  canSubscribe,
  ref: variantsRef,
}: Props) => {
  const { getText } = useLanguage();
  const { commentId, tab } = useSearch({ from: "/_app/campaign/$id/" });
  const [tabValue, setTabValue] = useState(tab || "introduction");
  const { ref } = useScrollInToView({ trigger: commentId });

  useEffect(() => {
    if (tab) {
      setTabValue(tab);
    }
  }, [tab]);

  const hasSubscribed = campaign.variants.some((v) => v.hasSubscribed);

  return (
    <div ref={ref}>
      <Tabs
        variant={"rounded"}
        value={tabValue}
        onValueChange={setTabValue}>
        <TabsList className="justify-center mx-auto sm:gap-x-4 md:flex-row flex-col md:w-fit w-full">
          <TabsTrigger
            value="introduction"
            className="h-[56px] px-[30px] md:w-max w-full [&>div]:w-full">
            <div className="flex md:justify-center justify-between items-center w-full md:gap-x-8">
              {getText("project_introduction")}
              <ArrowDown className="size-5" />
            </div>
          </TabsTrigger>
          <TabsTrigger
            value="activity"
            className="h-[56px] px-[30px] md:w-max w-full [&>div]:w-full">
            <div className="flex md:justify-center justify-between items-center w-full md:gap-x-8">
              {getText("activity_report")}
              <ArrowDown className="size-5" />
            </div>
          </TabsTrigger>
          <TabsTrigger
            value="supporters"
            className="h-[56px] px-[30px] md:w-max w-full [&>div]:w-full">
            <div className="flex md:justify-center justify-between items-center w-full md:gap-x-8">
              {getText("supporters")}
              <ArrowDown className="size-5" />
            </div>
          </TabsTrigger>
          <TabsTrigger
            value="variants"
            className="h-[56px] px-[30px] md:hidden flex w-full [&>div]:w-full">
            <div className="flex justify-between items-center w-full ">
              {getText("choose_return_plan")}
              <ArrowDown className="size-5" />
            </div>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="introduction">
          <article className="pt-16 grid gap-y-10">
            <DisplayTag text="project_introduction" />
            <MarkDown
              markdown={campaign?.description}
              className="text-font"
            />
          </article>
        </TabsContent>
        <TabsContent value="activity">
          <div className="pt-16 grid gap-y-10">
            <DisplayTag text="activity_report" />
            <Suspense
              fallback={
                <div className="sm:space-y-10 space-y-8">
                  <PostCardSkeleton />
                  <PostCardSkeleton />
                  <PostCardSkeleton />
                </div>
              }>
              <PostList
                vtuberId={campaign?.vtuber?.id!}
                campaignId={campaign.id}
                hasSubscribed={hasSubscribed}
                isPlanActive={campaign.vtuber?.isPlanActive!}
              />
            </Suspense>
          </div>
        </TabsContent>
        <TabsContent value="supporters">
          <div className="pt-16 grid gap-y-10">
            <DisplayTag text="supporters" />
            <CampaignSupporters
              campaignId={campaign.id}
              tabValue={tabValue}
            />
          </div>
        </TabsContent>
        <TabsContent
          value="variants"
          className="md:hidden block">
          <div
            className="pt-16"
            ref={variantsRef}>
            <CampaignVariantsList
              canSubscribe={canSubscribe}
              variants={campaign?.variants}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
