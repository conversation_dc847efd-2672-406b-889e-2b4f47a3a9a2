import { GetCampaignById } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import {
  Carousel,
  CarouselContent,
  CarouselIndicators,
  CarouselItem,
} from "@vtuber/ui/components/carousel";
import { Image } from "@vtuber/ui/components/image";
import { MediaModal } from "@vtuber/ui/components/media-modal";

export const CampaignDetailsBanner = ({
  campaign,
}: {
  campaign: GetCampaignById;
}) => {
  return (
    <Carousel>
      <CarouselContent>
        {campaign?.banners.map((img, i) => (
          <CarouselItem key={img + i.toString()}>
            <MediaModal
              thumbnails={campaign?.banners.map((img) => img.image || "")}
              currentSrc={img.image}
              alt={"campaign-baner"}
              className="size-full">
              <AspectRatio ratio={16 / 9}>
                <Image
                  src={img.image || ""}
                  alt={"campaign-banner"}
                  className="h-full rounded-10 w-full"
                />
              </AspectRatio>
            </MediaModal>
          </CarouselItem>
        ))}
      </CarouselContent>
      <div className="grid gap-y-6 pt-6">
        <CarouselIndicators
          className="rounded-10 lg:basis-[28%] sm:basis-1/5 xs:basis-1/3 basis-[45%] overflow-hidden"
          wrapperClassName="md:gap-x-6 justify-start"
          thumbnails={campaign?.banners.map((img) => img.image || "")}
        />
        <CarouselIndicators
          variant="dots"
          options={{
            align: "start",
            startIndex: 0,
          }}
          activeClassName="bg-white"
          className="bg-[#393847]"
          wrapperClassName="m-0"
        />
      </div>
    </Carousel>
  );
};
