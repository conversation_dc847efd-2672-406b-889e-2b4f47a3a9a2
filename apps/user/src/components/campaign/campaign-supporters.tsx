import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useQuery } from "@tanstack/react-query";
import { getRouteApi } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Separator } from "@vtuber/ui/components/separator";
import { useState } from "react";
import { campaignSupportersQueryOptions } from "~/utils/api";
import { LoadMoreButton } from "../load-more-button";
import { CampaignSupportersSkeleton } from "../skeletons/campaign-supporters-skeleton";

interface Props {
  campaignId: string;
  tabValue: string;
}
export const CampaignSupporters = ({ campaignId, tabValue }: Props) => {
  const Route = getRouteApi("/_app/campaign/$id/");
  const { campaignSubscribers } = Route.useLoaderData();
  const [size, setSize] = useState(10);
  const { getText } = useLanguage();
  const {
    data: subscribers,
    isPending,
    isRefetching,
  } = useQuery({
    ...campaignSupportersQueryOptions({
      campaignId,
      size,
    }),
    enabled: !!campaignId && tabValue === "supporters",
    initialData: campaignSubscribers,
  });

  if (isPending) return <CampaignSupportersSkeleton />;

  if (!subscribers || subscribers.data.length === 0)
    return (
      <div>
        <div className="text-center py-12 bg-sub rounded-lg">
          <p className="text-muted-foreground">
            {getText("No_Subscribers_Yet")}
          </p>
        </div>
      </div>
    );
  return (
    <div className="grid gap-y-10">
      {subscribers?.data.map((s) => (
        <div
          key={s.id}
          className="text-font grid gap-y-6">
          <div className="grid gap-y-4">
            <div className="flex sm:items-center items-start sm:justify-between sm:flex-row flex-col sm:gap-y-0 gap-y-[11px]">
              <div className="flex items-center gap-x-3">
                <Avatar
                  className="size-[45px]"
                  fallback={s.user?.name}
                  alt={s.user?.name}
                  src={s.user?.image}
                />
                <p className="font-medium">{s.user?.name}</p>
              </div>
              <p className="text-sm font-medium">
                {timestampDate(s.createdAt!).toLocaleDateString()}
              </p>
            </div>
            <p>{s.comment}</p>
          </div>
          <Separator />
        </div>
      ))}
      {isRefetching && <CampaignSupportersSkeleton length={3} />}
      <LoadMoreButton
        itemsLength={subscribers?.data?.length || 0}
        totalItems={subscribers?.paginationDetails?.totalItems || 0}
        onClick={() => setSize(size + 5)}
      />
    </div>
  );
};
