import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { Logo } from "@vtuber/ui/components/logo";
import { TextAnimate } from "@vtuber/ui/components/text-animate";
import { motion } from "motion/react";

export const HomeBanner = () => {
  const router = useRouter();
  const { session } = useAuth();
  const { getText, language } = useLanguage();

  return (
    <Container className="flex flex-col items-center flex-1 justify-center gap-y-6 pb-16">
      <div className="text-center space-y-6">
        <h1
          className="sm:text-[43px] text-[22px] font-extrabold text-black leading-[140%]"
          style={{
            filter:
              "drop-shadow(2px 3px 0px #fff) drop-shadow(2px 3px 0px #fff) drop-shadow(2px -3px 5px #fff)",
          }}>
          <TextAnimate
            animation="blurInUp"
            by="character"
            as="span"
            className="italic"
            once>
            {getText("favorite")}
          </TextAnimate>{" "}
          <TextAnimate
            delay={0.3}
            animation="blurInUp"
            by="character"
            once
            as="span"
            className="sm:text-[33px] text-base italic">
            {getText("&")}
          </TextAnimate>{" "}
          <TextAnimate
            delay={0.4}
            animation="blurInUp"
            by="character"
            once
            as="span"
            className="sm:text-[68px] text-[33px] 
             inline-block skew-x-[-12deg] 
             [&>span]:text-transparent 
             [&>span]:bg-clip-text 
             [&>span]:bg-[linear-gradient(45deg,#ff9b4c,#ff6103)]">
            {getText("excitement")}
          </TextAnimate>
        </h1>
        <h2
          className="font-extrabold text-black sm:text-[43px] text-[22px] leading-[140%]"
          style={{
            filter:
              "drop-shadow(2px 3px 0px #fff) drop-shadow(2px 3px 0px #fff) drop-shadow(2px -3px 5px #fff)",
          }}>
          <TextAnimate
            animation="blurInUp"
            by="character"
            delay={0.6}
            as="span"
            className="italic"
            once>
            {language === "ja" ? "推し" : "Spend"}
          </TextAnimate>{" "}
          <TextAnimate
            animation="blurInUp"
            by="character"
            delay={0.7}
            as="span"
            once
            className="sm:text-[33px] text-base italic">
            {language === "ja" ? "との" : "a"}
          </TextAnimate>{" "}
          <TextAnimate
            animation="blurInUp"
            by="character"
            delay={0.8}
            as="span"
            once
            className="sm:text-[68px] text-[33px] [&>span]:text-transparent [&>span]:bg-clip-text [&>span]:bg-[linear-gradient(45deg,#A470CC,#7EFFFE)] inline-block skew-x-[-12deg]">
            {language === "ja" ? "特別な瞬間" : "Special Moment"}
          </TextAnimate>{" "}
          <TextAnimate
            animation="blurInUp"
            by="character"
            delay={0.9}
            as="span"
            once
            className="sm:text-[33px] text-base italic">
            {language === "ja" ? "を過ごす" : "with your idol"}
          </TextAnimate>
        </h2>
      </div>
      <motion.div
        initial={{ translateY: 50, opacity: 0 }}
        animate={{ translateY: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 1.3 }}>
        <Logo
          height={"137"}
          width={"430"}
          className="sm:h-[137px] sm:w-[430px] h-[69px] w-[217px]"
        />
      </motion.div>
      <TextAnimate
        animation="fadeIn"
        by="line"
        as="p"
        delay={1.5}
        once
        className="font-bold sm:text-[29px] text-lg text-[#333333] text-center leading-[3rem]">
        {`${getText("home_banner_text_one")}\n\n${getText("home_banner_text_two")}`}
      </TextAnimate>
      {!session && (
        <motion.div
          initial={{ translateY: 50, opacity: 0 }}
          animate={{ translateY: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.8 }}
          className="flex items-center sm:flex-row flex-col gap-4 w-full justify-center">
          <Button
            onClick={() => {
              router.navigate({
                to: "/login",
              });
            }}
            variant={"sub-outline"}
            className="h-[54px] font-bold border border-primary sm:w-[268px] w-full shadow-none"
            style={{
              filter: "drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25))",
            }}>
            {getText("login")}
          </Button>
          <Button
            onClick={() => {
              router.navigate({
                to: "/register",
              });
            }}
            className="h-[54px] font-bold text-[#171616] sm:w-[268px] w-full shadow-none"
            style={{
              filter: "drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25))",
            }}>
            {getText("get_started_for_free")}
          </Button>
        </motion.div>
      )}
    </Container>
  );
};
