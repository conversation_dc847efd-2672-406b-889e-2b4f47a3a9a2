import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { VtuberProfile } from "@vtuber/services/vtubers";
import { buttonVariants } from "@vtuber/ui/components/button";
import {
  Carousel,
  CarouselContent,
  CarouselIndicators,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@vtuber/ui/components/carousel";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { cn } from "@vtuber/ui/lib/utils";
import { ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import { VtuberCard } from "../vtuber/vtuber-card";

export const NewVtuberList = ({ vtubers }: { vtubers: VtuberProfile[] }) => {
  const { getText } = useLanguage();

  if (!vtubers || vtubers.length === 0) return null;

  return (
    <section className="md:space-y-16">
      <Container>
        <ContentHeading
          title="新着Vtuber"
          subTitle="New Vtubers"
        />
      </Container>

      <Carousel
        opts={{
          loop: false,
        }}>
        <CarouselContent>
          {vtubers.map((vtuber) => (
            <CarouselItem
              key={vtuber.id}
              className="basis-1/3">
              <VtuberCard
                vtuber={vtuber}
                className="[&_p]:bg-transparent [&_p]:font-bold [&_p]:text-lg rounded-[20px]"
                key={vtuber.id.toString()}
              />
            </CarouselItem>
          ))}
        </CarouselContent>

        <div className="md:pt-6 flex flex-row justify-center md:gap-x-0 gap-x-[18px] !items-center">
          <CarouselPrevious className="!static border-[#2A8796] size-[50px] md:hidden flex translate-y-0">
            <ChevronLeft className="text-[#2A8796] !size-3" />
          </CarouselPrevious>
          <CarouselIndicators
            variant="dots"
            wrapperClassName="mt-0"
          />
          <CarouselNext className="!static border-[#2A8796] size-[50px] md:hidden flex translate-y-0">
            <ChevronRight className="text-[#2A8796] !size-3" />
          </CarouselNext>
        </div>
      </Carousel>
      <div className="flex justify-center md:pt-0 pt-10">
        <Link
          to="/vtuber"
          className={cn(
            buttonVariants({
              variant: "outline",
              size: "xl",
            }),
            "flex items-center gap-x-8 font-medium rounded-full",
          )}>
          {getText("see_more_vtubers")} <ArrowRight />
        </Link>
      </div>
    </section>
  );
};
