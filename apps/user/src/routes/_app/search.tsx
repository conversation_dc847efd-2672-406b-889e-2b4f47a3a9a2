import { createFileRoute } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { cn } from "@vtuber/ui/lib/utils";
import { useState } from "react";
import { z } from "zod";
import { CallToAction } from "~/components/layout/call-to-action";
import { PageTitle } from "~/components/layout/page-title";
import { SearchBar } from "~/components/layout/search-bar";

export const Route = createFileRoute("/_app/search")({
  component: RouteComponent,
  validateSearch: z.object({
    query: z.string().optional(),
  }),
});

function RouteComponent() {
  const { getText, language } = useLanguage();
  const { query } = Route.useSearch();
  const [type, setType] = useState("vtuber");
  // NOTE: if results are then space-y-[71px] or space-y-[68px]
  return (
    <div className="pt-20">
      <Container className="pb-40">
        <PageTitle title="site_search" />
        <div className="space-y-[71px] pt-20">
          <SearchBar
            className="lg:max-w-[1000px] mx-auto"
            categories={[
              "#3Dモデル制作",
              "#衣装制作",
              "#Live2D制作",
              "#歌手",
              "#男性",
              "#女性",
            ]}
          />
          <h3 className="font-bold text-[28px] text-font">
            {language === "ja"
              ? `"${query}" の検索結果`
              : `Search results for "${query}"`}
          </h3>
          {/* <SearchNoResultsMessage /> */}
          <section className="flex items-center gap-x-[59px]">
            <Button
              onClick={() => {
                setType("vtuber");
              }}
              variant={type === "vtuber" ? "purple" : "white-outline"}
              className="h-[58px] px-[46px] text-lg font-medium">
              <p>
                VTuber <span className="text-[29px]">20</span>件
              </p>
            </Button>
            <Button
              onClick={() => {
                setType("crowdfunding");
              }}
              variant={"white-outline"}
              className={cn(
                "h-[58px] px-[46px] text-lg font-medium",
                type === "crowdfunding"
                  ? "bg-[#1F8289] hover:bg-transparent hover:text-white"
                  : "",
              )}>
              <p>
                クラウドファンディング <span className="text-[29px]">12</span>件
              </p>
            </Button>
          </section>
          <section className="space-y-6">
            <DisplayTag
              type="underlined"
              className="capitalize"
              text={type === "vtuber" ? "Vtuber" : "crowdfunding"}
            />
            <p className="font-medium text-font">
              <span className="text-tertiary">1-20</span>件表示 / 246件中
            </p>
          </section>
          <section className="text-5xl">Search results</section>
        </div>
      </Container>
      <CallToAction />
    </div>
  );
}
