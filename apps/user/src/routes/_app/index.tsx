import { Await, createFileRoute } from "@tanstack/react-router";
import {
  campaignClient,
  eventClient,
  vtuberProfilesClient,
} from "@vtuber/services/client";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { GradientShape } from "@vtuber/ui/components/shape/gradient";
import { OutlineRectangle } from "@vtuber/ui/components/shape/outline-rectangle";
import { z } from "zod";
import { CategorySearch } from "~/components/category-search";
import { AboutVsai } from "~/components/home/<USER>";
import { HomeBanner } from "~/components/home/<USER>";
import { HomeCampaignList } from "~/components/home/<USER>";
import { HomeComingSoonCampaigns } from "~/components/home/<USER>";
import { HomeEvents } from "~/components/home/<USER>";
import { HomeRecommendedCampaigns } from "~/components/home/<USER>";
import { NewVtuberList } from "~/components/home/<USER>";
import { CallToAction } from "~/components/layout/call-to-action";
import { KeywordSearch } from "~/components/layout/keyword-search";
import { HomeCampaignListSkeleton } from "~/components/skeletons/home-campaign-list-skeleton";
import { HomeComingSoonCampaignsSkeleton } from "~/components/skeletons/home-comingsoong-campaigns-skeleton";
import { HomeEventsSkeleton } from "~/components/skeletons/home-events-skeleton";
import { HomeRecommendedCampaignSkeleton } from "~/components/skeletons/home-recommended-campaign-skeleton-list";
import { HomeVtuberListSkeleton } from "~/components/skeletons/home-vtuber-list-skeleton";
export const Route = createFileRoute("/_app/")({
  component: RouteComponent,
  validateSearch: z.object({
    page: z.number().optional(),
    size: z.number().optional(),
  }),
  loader: async () => {
    const campaignPromise = campaignClient.getAllCampaigns({
      pagination: {
        size: 20,
      },
    });

    const comingSoonCampaignPromise = campaignClient.getAllCampaigns({
      pagination: {
        size: 20,
        sort: "created_at",
        order: "ASC",
      },
    });

    const vtuberPromise = vtuberProfilesClient.getAllVtuberProfiles({
      pagination: {
        size: 10,
      },
    });

    const eventsPromise = eventClient.getAllEvents({
      pagination: {
        size: 20,
      },
    });

    return {
      eventsPromise,
      vtuberPromise,
      campaignPromise,
      comingSoonCampaignPromise,
    };
  },
});

function RouteComponent() {
  const {
    eventsPromise,
    vtuberPromise,
    campaignPromise,
    comingSoonCampaignPromise,
  } = Route.useLoaderData();

  return (
    <main className="overflow-x-hidden">
      <section className="xl:min-h-[90dvh] min-h-[100dvh] relative bg-cover bg-no-repeat flex flex-col lg:bg-[url('https://cdn.v-sai.com/assets/home_banner.png')] md:bg-[url('https://cdn.v-sai.com/assets/home_banner_tablet.jpg')] bg-[url('https://cdn.v-sai.com/assets/home_banner_mobile.jpg')] pt-20">
        <div className="xl:pt-0 xl:pb-0 pb-12 pt-20 flex-1 flex-col flex items-center justify-center">
          <HomeBanner />
          <div className="xl:!absolute !static -bottom-32 w-full z-20">
            <Await
              promise={campaignPromise}
              fallback={<HomeCampaignListSkeleton />}
              children={([res, err]) => {
                if (err || !res.data || res.data.length === 0) return null;
                return <HomeCampaignList campaigns={res.data} />;
              }}
            />
          </div>
        </div>
      </section>
      <div className="md:space-y-36 space-y-24 py-20 md:pt-64 pt-24">
        <AboutVsai />
        <Container className="md:space-y-16 space-y-14">
          <div className="flex items-center relative justify-between md:pr-56">
            <ContentHeading
              title="キーワード検索"
              subTitle="Keyword Search"
            />
            <div className="relative sm:block hidden">
              <GradientShape className="blur-sm size-[76px]" />
              <OutlineRectangle className="absolute -top-2 size-9 -right-2 -z-10" />
            </div>
          </div>
          <KeywordSearch />
        </Container>
        <Await
          promise={campaignPromise}
          fallback={<HomeRecommendedCampaignSkeleton />}
          children={([res, err]) => {
            if (err || !res.data || res.data.length === 0) return null;
            return <HomeRecommendedCampaigns campaign={res.data} />;
          }}
        />
        <Await
          promise={comingSoonCampaignPromise}
          fallback={<HomeComingSoonCampaignsSkeleton />}
          children={([res, err]) => {
            if (err || !res.data || res.data.length === 0) return null;
            return <HomeComingSoonCampaigns campaigns={res.data} />;
          }}
        />
      </div>
      <div className="md:space-y-36 space-y-0 md:pt-12">
        <Await
          promise={vtuberPromise}
          fallback={<HomeVtuberListSkeleton />}
          children={([res, err]) => {
            if (err || !res.data || res.data.length === 0) return null;
            return <NewVtuberList vtubers={res.data} />;
          }}
        />
        {/* <Await
          fallback={<PopularCampaignSkeleton />}
          promise={popularCampaignPromise}
          children={([res, err]) => {
            if (err) return null;
            return (
              <div className="md:pt-0 pt-12">
                <PopularCampaign campaign={res} />
              </div>
            );
          }}
        /> */}
        <div className="md:space-y-36 space-y-24">
          <Await
            promise={eventsPromise}
            fallback={<HomeEventsSkeleton />}
            children={([res, err]) => {
              if (err || !res.data || res.data.length === 0) return null;
              return <HomeEvents events={res.data} />;
            }}
          />
          <Container className="sm:space-y-16 space-y-14">
            <ContentHeading
              title="カテゴリ検索"
              subTitle="Category Search"
            />
            <CategorySearch />
          </Container>
        </div>
      </div>
      <div className="md:pt-48 pt-32">
        <CallToAction />
      </div>
    </main>
  );
}
