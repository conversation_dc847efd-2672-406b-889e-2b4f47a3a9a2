import { createFileRoute, <PERSON>, useNavigate } from "@tanstack/react-router";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { InputBadge } from "@vtuber/ui/components/input-badge";
import { ArrowRight, Triangle } from "lucide-react";
import { useForm } from "react-hook-form";
import { PageTitle } from "~/components/layout/page-title";

export const Route = createFileRoute("/_app/contact/")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();
  const form = useForm({
    defaultValues: {
      companyName: "",
      name: "",
      projectUrl: "",
      inquiryType: "",
      email: "",
      emailConfirm: "",
      phoneNo: "",
      inquiryDetails: "",
    },
  });
  return (
    <Container className="pt-20">
      <PageTitle title="contact_page_title" />
      <div className="md:max-w-[824px] mx-auto w-full grid gap-y-[35px] text-font sm:pt-20 pt-10">
        <h3 className="sm:text-lg text-sm leading-[180%]">
          リターン内容や配送確認、プロジェクトに関するご質問は、以下のフォームよりご利用ください。ご不明な点がありましたら、お問い合わせ前に{" "}
          「<span className="text-blue01">よくある質問</span>」
          をご確認ください。
        </h3>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((v) => {
              navigate({
                to: "/contact/thanks",
              });
            })}
            className="gap-y-[72px] grid">
            <div className="grid gap-y-[35px]">
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="companyName"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder="例) V祭"
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">会社名</p>
                      <InputBadge />
                    </div>
                  }
                />
                <ul className="list-disc pl-5 text-sm space-y-1">
                  <li>
                    個人の場合は、「タレント名」または「個人」とご記載ください。
                  </li>
                  <li>
                    事務所や企業の方は、所属事務所名や企業名などご記載ください。
                  </li>
                </ul>
              </div>
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder="例) V祭 太郎"
                name="name"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">お名前</p>
                    <InputBadge />
                  </div>
                }
              />
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder="例) プロジェクトのURLを入力ください。"
                name="projectUrl"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">プロジェクトのURL</p>
                    <InputBadge type="optional" />
                  </div>
                }
              />
              <SelectInput
                Icon={Triangle}
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder="ーお問い合わせ種類を選択してください"
                name="inquiryType"
                className="rounded-xs h-[50px] placeholder:text-[#505050] [&_svg]:fill-font [&_svg]:rotate-180"
                options={[
                  {
                    label: "キャンペーン内容について",
                    value: "キャンペーン内容について",
                  },
                  {
                    label: "リターン品について",
                    value: "リターン品について",
                  },
                  {
                    label: "プラン購入について",
                    value: "プラン購入について",
                  },
                  {
                    label: "その他質問",
                    value: "その他質問",
                  },
                ]}
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">お問い合わせ種類</p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="email"
                  type="email"
                  inputMode="email"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder="例) <EMAIL>"
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">メールアドレス</p>
                      <InputBadge />
                    </div>
                  }
                />
                <ul className="list-disc pl-5 text-sm space-y-1">
                  <li>入力したメールアドレスへのみ返信いたします。</li>
                  <li>
                    携帯キャリア（au、softbank、docomo）やicloudメール、outlookメールの場合、初期設定で迷惑メールフィルタが設定されており、メールが届かないことがあります。受信設定をご確認の上、お問い合わせください。
                  </li>
                  <li>
                    返信にお時間をいただく場合がありますので、予めご了承ください。
                  </li>
                  <li>
                    ドメイン指定をされている場合「@v-sai.jp」からのメールが受信できるよう設定をお願いいたします。
                  </li>
                  <li>
                    メールアドレスに2つ以上の連続したピリオド「…」や「@」の直前にピリオド「.」がある場合、正常に受信や返信ができません。別のメールアドレスからお問い合わせください。
                  </li>
                </ul>
              </div>
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                name="emailConfirm"
                type="email"
                inputMode="email"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                placeholder="例) <EMAIL>"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">確認用メールアドレス</p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="phoneNo"
                  type="tel"
                  inputMode="tel"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder="例) 090-1234-5678"
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">電話番号</p>
                      <InputBadge type="optional" />
                    </div>
                  }
                />
                <ul className="list-inside list-disc text-sm text-font">
                  <li>ハイフンあり でご入力ください。</li>
                </ul>
              </div>
              <TextAreaInput
                minHeight={215}
                className="rounded-xs placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">お問い合わせ内容</p>
                    <InputBadge />
                  </div>
                }
                control={form.control}
                name="inquiryDetails"
                placeholder="お問い合わせ内容を詳しくご記載ください。"
              />
            </div>
            <Button
              variant={"tertiary"}
              className="h-[68px] base:w-[336px] w-full mx-auto text-white block pl-[46px] pr-[30px] justify-between">
              <div className=" flex justify-between items-center w-full">
                <p className="font-bold text-lg">送信する</p>
                <ArrowRight />
              </div>
            </Button>
          </form>
        </Form>
        <div className="bg-[#2C2820] sm:p-8 p-4 rounded-xs leading-[180%]">
          <h6>必ず送信前にお読みください</h6>
          <ul className="list-disc text-[#CCCCCC] font-medium pl-5">
            <li>
              リターンに関するお問い合わせは、リターン実施はプロジェクトオーナー様の責務になるため、各プロジェクトページ内の「オーナーへのお問い合わせ」よりお問い合わせください。利用規約(第11条
              免責)をご確認ください。
            </li>
            <li>
              テレワークを実施中のため、随時メールにてご連絡させていただきます。お問い合わせの内容によっては返信に時間を要する場合があるため、予めご了承ください。
            </li>
            <li>
              事前に「
              <Link
                className="text-blue01"
                to="/faq">
                よくあるご質問
              </Link>
              」をご確認ください。
            </li>
          </ul>
        </div>
      </div>
    </Container>
  );
}
