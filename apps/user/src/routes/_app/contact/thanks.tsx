import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { cn } from "@vtuber/ui/lib/utils";
import { CallToAction } from "~/components/layout/call-to-action";

export const Route = createFileRoute("/_app/contact/thanks")({
  component: RouteComponent,
});

function RouteComponent() {
  const { getText } = useLanguage();
  return (
    <div>
      <Container className="text-font flex flex-col items-center py-40 gap-y-16">
        <h1 className="text-center font-bold text-[40px] w-fit mx-auto bg-clip-text text-transparent bg-gradient-text">
          お問い合わせいただきありがとうございます。
        </h1>
        <section className="gap-y-[56px] flex flex-col items-center">
          <div className="text-lg text-left leading-[180%] space-y-10">
            <p>
              運営へのお問い合わせを受け付けました。お問い合わせ内容を確認させていただき、メールにて回答{" "}
              <br />
              いたします。送信内容は、確認のためご入力のメールアドレスへ送信しております。
            </p>
            <p>
              万が一、メールが届いていない場合は、メールアドレスの記入ミスや、迷惑メール等の受信設定され
              <br />
              ている可能性があるため、再度お問い合わせください。
            </p>
          </div>
          <Link
            to="/"
            className={cn(
              buttonVariants({
                variant: "outline",
              }),
              "font-bold rounded-full h-[60px] w-[424px] mx-auto",
            )}>
            {getText("back_to_top")}
          </Link>
        </section>
      </Container>
      <CallToAction />
    </div>
  );
}
