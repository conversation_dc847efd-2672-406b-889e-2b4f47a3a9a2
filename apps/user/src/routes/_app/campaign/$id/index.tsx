import { timestampDate } from "@bufbuild/protobuf/wkt";
import {
  createFileRoute,
  Link,
  notFound,
  useNavigate,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { campaignClient } from "@vtuber/services/client";
import { Button, buttonVariants } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { HeaderBreadCrumb } from "@vtuber/ui/components/header-bread-crumb";
import { FacebookIcon } from "@vtuber/ui/components/icons/facebook-icon";
import { XIcon } from "@vtuber/ui/components/icons/x-icon";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { cn, getRemainingDays } from "@vtuber/ui/lib/utils";
import { ArrowDown, ArrowRight } from "lucide-react";
import { useRef } from "react";
import { string, z } from "zod";
import { CampaignDetailsBanner } from "~/components/campaign/campaign-details-banner";
import { CampaignDetailsCard } from "~/components/campaign/campaign-details-card";
import { CampaignDetailsHeader } from "~/components/campaign/campaign-details-header";
import { CampaignOverviewTabs } from "~/components/campaign/campaign-overview-tabs";
import { CampaignVariantsList } from "~/components/campaign/campaign-variants-list";
import { CampaignVtuberDetails } from "~/components/campaign/campaign-vtuber-details";
import { CallToAction } from "~/components/layout/call-to-action";
import { vtuberPostQueryOptions } from "~/utils/api";
import { seo } from "~/utils/seo";

export const Route = createFileRoute("/_app/campaign/$id/")({
  component: RouteComponent,
  validateSearch: z.object({
    commentId: z.number().optional(),
    tab: string().optional(),
  }),
  loader: async ({ params, context }) => {
    const [campaign, err] = await campaignClient.getCampaignById({
      id: params.id,
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    await context.queryClient.prefetchInfiniteQuery(
      vtuberPostQueryOptions({
        campaignId: campaign.data?.id!,
        size: 3,
      }),
    );
    const [campaignSubscribers] =
      await campaignClient.getCampaignSubscriberComments({
        campaignId: campaign.data?.id!,
      });

    const [relatedCampaigns] = await campaignClient.getRelatedCampaign({
      campaignId: campaign.data?.id!,
    });

    return {
      campaign: campaign.data,
      relatedCampaigns: relatedCampaigns?.data,
      campaignSubscribers: campaignSubscribers,
    };
  },

  staleTime: 60000,
  head: ({ loaderData }) => {
    const campaign = loaderData?.campaign;
    return {
      meta: seo({
        title: "V-SAI" + " | " + campaign?.name!,
        description: campaign?.shortDescription!,
        url: `/campaign/${campaign?.slug}`,
        image: campaign?.banners[0]?.image,
        keywords:
          "VTuber, V-SAI, V-SAI - meet your favorite VTubers, crowdfunding, VTuber crowdfunding",
      }),
    };
  },
});

function RouteComponent() {
  const { isMobile, isTablet } = useIsMobile();
  const variantsRef = useRef<HTMLDivElement | null>(null);
  const { getText } = useLanguage();
  const navigate = useNavigate({ from: Route.fullPath });
  const { campaign, relatedCampaigns } = Route.useLoaderData();

  const viewVariants = (search: boolean = true) => {
    if (!search) {
      variantsRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      return;
    }
    navigate({
      search: (old) => ({
        ...old,
        tab: "variants",
      }),
    });

    setTimeout(() => {
      variantsRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }, 60);
  };

  if (!campaign)
    return (
      <RouteFallback
        className="pt-20"
        data={{
          data: "sorry, we couldn't find the campaign you're looking for.",
        }}
      />
    );
  const hasCampaignStarted =
    timestampDate(campaign.startDate!).getTime() < Date.now();
  const hasCampaignEnded = getRemainingDays(campaign.endDate!) === 0;
  const isPlanActive = campaign.vtuber?.isPlanActive;

  const canSubscribe = hasCampaignStarted && !hasCampaignEnded;

  return (
    <div className="md:pt-28 pt-[92px]">
      <Container className="pb-20">
        <HeaderBreadCrumb className="md:hidden flex" />
        <div className="flex flex-col gap-y-32 md:pt-0 pt-10">
          <div className="grid grid-cols-12 items-start">
            <section className="md:col-span-7 col-span-12 flex flex-col gap-y-[72px]">
              <CampaignDetailsHeader campaign={campaign} />
              <CampaignDetailsBanner campaign={campaign} />
              <div className="flex sm:flex-row flex-col items-center justify-center sm:gap-x-10 sm:gap-y-0 gap-y-6">
                <Button
                  disabled={!canSubscribe}
                  onClick={() => {
                    if (isTablet || isMobile) return viewVariants(true);

                    viewVariants(false);
                  }}
                  className="h-[56px] px-[22px] sm:w-max w-full [&>div]:gap-x-8 text-white font-bold"
                  variant={"tertiary"}>
                  <p>リターンのプランを選択する</p>
                  <ArrowDown className="size-5" />
                </Button>
                <Link
                  to="/campaign/$id/contact"
                  params={{
                    id: campaign.slug,
                  }}
                  className={cn(
                    buttonVariants({
                      variant: "outline",
                    }),
                    "h-[56px] px-[22px] [&>div]:gap-x-8 font-bold sm:w-max w-full",
                  )}>
                  <p>オーナーへお問い合わせ</p>
                  <ArrowRight className="size-5" />
                </Link>
              </div>
              <MarkDown
                markdown={campaign?.shortDescription}
                className="text-font"
              />
              <ExternalLinks
                className="bg-sub py-[10px] flex items-center md:justify-center gap-x-3"
                socialMediaLinks={campaign.socialMediaLinks}
              />
              {!!campaign?.promotionalMessage && (
                <p className="text-font">{campaign.promotionalMessage}</p>
              )}
              <CampaignDetailsCard
                canSubscribe={canSubscribe}
                className="md:hidden grid"
                campaign={campaign}
                onClick={viewVariants}
                isPlanActive={isPlanActive}
              />
              <CampaignVtuberDetails
                className="md:hidden block"
                campaign={campaign}
              />
              <CampaignOverviewTabs
                canSubscribe={canSubscribe}
                campaign={campaign}
                variantsRef={variantsRef}
              />
              <div className="flex items-center sm:flex-row flex-col sm:gap-x-10 sm:gap-y-0 gap-y-4">
                <Button className="w-full bg-sub px-8 h-[56px] [&>div]:gap-x-8 font-bold hover:bg-white/10 border-2 border-sub text-white">
                  <XIcon />
                  <p>Xでシェア</p>
                </Button>
                <Button className="w-full bg-sub px-8 h-[56px] [&>div]:gap-x-8 font-bold hover:bg-white/10 border-2 border-sub text-white">
                  <FacebookIcon className="[&>path:first-child]:fill-white [&>path:last-child]:fill-transparent !size-[22px]" />
                  <p>facebookでシェア</p>
                </Button>
              </div>
            </section>
            <section className="col-span-5 md:block hidden">
              <div className="max-w-[352px] ml-auto grid gap-y-10">
                <CampaignDetailsCard
                  campaign={campaign}
                  onClick={viewVariants}
                  canSubscribe={canSubscribe}
                  isPlanActive={isPlanActive}
                />
                <CampaignVtuberDetails campaign={campaign} />
                <div ref={variantsRef}>
                  <CampaignVariantsList
                    canSubscribe={canSubscribe}
                    variants={campaign?.variants}
                  />
                </div>
              </div>
            </section>
          </div>
          {/* {relatedCampaigns && relatedCampaigns?.length > 0 && (
            <section className="grid gap-y-10">
              <h3 className="sm:text-4xl text-[22px] font-bold text-font">
                {getText("popular_campaign")}
              </h3>
              <Carousel className="md:flex hidden">
                <CarouselContent className="gap-x-2">
                  {relatedCampaigns?.map((c) => (
                    <CarouselItem
                      key={c.id}
                      className="basis-[28%]">
                      <CampaignCard campaign={c} />
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
              <div className="md:hidden grid grid-cols-2 gap-y-9 gap-x-6">
                {relatedCampaigns?.map((c) => (
                  <CampaignCard
                    key={c.id}
                    campaign={c}
                  />
                ))}
              </div>
            </section>
          )} */}
        </div>
      </Container>
      <CallToAction />
    </div>
  );
}
